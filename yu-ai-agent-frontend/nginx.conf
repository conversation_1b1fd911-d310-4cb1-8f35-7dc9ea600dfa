server {
    listen       80;
    server_name  localhost;
    
    # 前端静态文件根目录
    root   /usr/share/nginx/html;

    # 所有HTML请求都返回index.html（解决Vue路由的404问题）
    location / {
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # API请求反向代理到指定后端
    location ^~ /api/ {
        # 指定后端地址
        proxy_pass https://www.codefather.cn/api/;
        
        # 设置请求头
        proxy_set_header Host www.codefather.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 修复SSE (Server-Sent Events)配置
        proxy_set_header Connection ""; # 保持连接打开
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_cache off;
        chunked_transfer_encoding off;
        proxy_read_timeout 600s;
        
        # 增加错误调试信息
        proxy_intercept_errors off;
    }

    # 处理静态资源请求
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public";
    }

    # 处理错误页
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
} 